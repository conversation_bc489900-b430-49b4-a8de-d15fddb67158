<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jnpf-java-boot</artifactId>
        <groupId>com.jnpf</groupId>
        <version>3.5.0-RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>jnpf-admin</artifactId>
    <packaging>jar</packaging>
    <!--打包WAR包删除注释-->
    <!--<packaging>war</packaging>-->

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-file-controller</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-extend-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-form-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-system-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-scheduletask-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-message-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-permission-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdev-base-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdev-onlinedev-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdev-generater-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdev-portal-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdata-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-exception</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-workflow-engine-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-workflow-form-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-oauth-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-example-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-app-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdev-integrate-controller</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- OA模块 -->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-oa</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- device模块 -->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-device</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--  IOT模块-->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-iot</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--  临时用电模块-->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-tpsr</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 车辆管理模块 -->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-vehicle</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 第三方接口对接 -->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-api-third</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- SpringBoot Admin 客户端  -->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.github.jsqlparser</groupId>-->
        <!--            <artifactId>jsqlparser</artifactId>-->
        <!--            <version>4.7</version>-->
        <!--        </dependency>-->

        <!-- quartz定时任务  -->
        <!--        <dependency>-->
        <!--            <groupId>org.quartz-scheduler</groupId>-->
        <!--            <artifactId>quartz</artifactId>-->
        <!--            <version>2.3.0</version>-->
        <!--        </dependency>-->

        <!--打包WAR包删除注释-->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>-->

    </dependencies>

    <build>
        <plugins>
            <!-- ... -->
            <!--打包WAR包注释插件-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>jnpf.JnpfAdminApplication</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- ... -->
            <!--打包WAR包删除注释-->
            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.2</version>
                <configuration>
                    <warSourceExcludes>src/main/resources/**</warSourceExcludes>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>-->
        </plugins>
    </build>
</project>
