-- 用车申请表
CREATE TABLE `zz_vehicle_application` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `application_no` varchar(50) NOT NULL COMMENT '申请单号（系统自动生成）',
  `applicant_id` varchar(50) NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(100) DEFAULT NULL COMMENT '申请人姓名',
  `apply_department` varchar(100) NOT NULL COMMENT '申请部门/队部',
  `use_date` datetime NOT NULL COMMENT '用车日期',
  `estimated_duration` decimal(10,2) DEFAULT NULL COMMENT '预计用车时长（小时）',
  `use_purpose` varchar(500) NOT NULL COMMENT '用车目的',
  `priority` varchar(10) DEFAULT '1' COMMENT '优先级：1-一般，2-紧急',
  `status` varchar(10) DEFAULT '1' COMMENT '申请状态：1-待审核，2-已审核，3-已分配，4-使用中，5-已完成，6-已拒绝',
  `assigned_vehicle_id` varchar(50) DEFAULT NULL COMMENT '分配车辆ID',
  `assigned_vehicle_number` varchar(50) DEFAULT NULL COMMENT '分配车辆车牌号',
  `team_leader_opinion` varchar(500) DEFAULT NULL COMMENT '队长审核意见',
  `team_leader_review_time` datetime DEFAULT NULL COMMENT '队长审核时间',
  `engineering_dept_opinion` varchar(500) DEFAULT NULL COMMENT '工程部审核意见',
  `engineering_dept_review_time` datetime DEFAULT NULL COMMENT '工程部审核时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际使用开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际使用结束时间',
  `actual_duration` decimal(10,2) DEFAULT NULL COMMENT '实际使用时长（台班）',
  `f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
  `f_creator_user_id` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `f_last_modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `f_last_modify_user_id` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  `f_delete_user_id` varchar(50) DEFAULT NULL COMMENT '删除用户',
  `f_delete_mark` int(1) DEFAULT '0' COMMENT '删除标志（0为未删，1为已删）',
  `f_tenant_id` varchar(50) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_application_no` (`application_no`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_apply_department` (`apply_department`),
  KEY `idx_status` (`status`),
  KEY `idx_use_date` (`use_date`),
  KEY `idx_assigned_vehicle_id` (`assigned_vehicle_id`),
  KEY `idx_creator_time` (`f_creator_time`),
  KEY `idx_delete_mark` (`f_delete_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用车申请表';

-- 插入测试数据（可选）
INSERT INTO `zz_vehicle_application` (
  `id`, `application_no`, `applicant_id`, `applicant_name`, `apply_department`, 
  `use_date`, `estimated_duration`, `use_purpose`, `priority`, `status`,
  `f_creator_time`, `f_creator_user_id`, `f_delete_mark`
) VALUES 
(
  '1', 'VA202412220001', 'user001', '张三', '工程部',
  '2024-12-23 08:00:00', 4.00, '运输建筑材料', '1', '1',
  NOW(), 'admin', 0
),
(
  '2', 'VA202412220002', 'user002', '李四', '施工队', 
  '2024-12-23 14:00:00', 2.00, '紧急物资运输', '2', '1',
  NOW(), 'admin', 0
);
