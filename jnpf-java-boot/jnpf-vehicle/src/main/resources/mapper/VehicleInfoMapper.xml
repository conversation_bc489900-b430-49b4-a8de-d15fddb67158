<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.vehicle.mapper.VehicleInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="jnpf.vehicle.entity.VehicleInfoEntity">
        <id column="id" property="id" />
        <result column="vehicle_number" property="vehicleNumber" />
        <result column="vehicle_model" property="vehicleModel" />
        <result column="vehicle_use_unit" property="vehicleUseUnit" />
        <result column="tonnage" property="tonnage" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="f_creator_time" property="creatorTime" />
        <result column="f_creator_user_id" property="creatorUserId" />
        <result column="f_last_modify_time" property="lastModifyTime" />
        <result column="f_last_modify_user_id" property="lastModifyUserId" />
        <result column="f_delete_time" property="deleteTime" />
        <result column="f_delete_user_id" property="deleteUserId" />
        <result column="f_delete_mark" property="deleteMark" />
        <result column="f_tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, vehicle_number, vehicle_model, vehicle_use_unit, tonnage, status, remark,
        f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id,
        f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id
    </sql>

    <!-- 根据车牌号查询车辆信息 -->
    <select id="getByVehicleNumber" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM zz_vehicle_info
        WHERE vehicle_number = #{vehicleNumber}
        AND f_delete_mark = 0
    </select>



    <!-- 统计各状态车辆数量 -->
    <select id="getStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            CASE 
                WHEN status = '1' THEN '可用'
                WHEN status = '2' THEN '维修中'
                WHEN status = '3' THEN '报废'
                ELSE '未知'
            END as statusName
        FROM zz_vehicle_info
        WHERE f_delete_mark = 0
        GROUP BY status
        ORDER BY status
    </select>

</mapper>
