<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.vehicle.mapper.VehicleApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="jnpf.vehicle.entity.VehicleApplicationEntity">
        <id column="id" property="id" />
        <result column="application_no" property="applicationNo" />
        <result column="applicant_id" property="applicantId" />
        <result column="applicant_name" property="applicantName" />
        <result column="apply_department" property="applyDepartment" />
        <result column="use_date" property="useDate" />
        <result column="estimated_duration" property="estimatedDuration" />
        <result column="use_purpose" property="usePurpose" />
        <result column="priority" property="priority" />
        <result column="status" property="status" />
        <result column="assigned_vehicle_id" property="assignedVehicleId" />
        <result column="assigned_vehicle_number" property="assignedVehicleNumber" />
        <result column="team_leader_opinion" property="teamLeaderOpinion" />
        <result column="team_leader_review_time" property="teamLeaderReviewTime" />
        <result column="engineering_dept_opinion" property="engineeringDeptOpinion" />
        <result column="engineering_dept_review_time" property="engineeringDeptReviewTime" />
        <result column="actual_start_time" property="actualStartTime" />
        <result column="actual_end_time" property="actualEndTime" />
        <result column="actual_duration" property="actualDuration" />
        <result column="f_creator_time" property="creatorTime" />
        <result column="f_creator_user_id" property="creatorUserId" />
        <result column="f_last_modify_time" property="lastModifyTime" />
        <result column="f_last_modify_user_id" property="lastModifyUserId" />
        <result column="f_delete_time" property="deleteTime" />
        <result column="f_delete_user_id" property="deleteUserId" />
        <result column="f_delete_mark" property="deleteMark" />
        <result column="f_tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, application_no, applicant_id, applicant_name, apply_department, use_date,
        estimated_duration, use_purpose, priority, status, assigned_vehicle_id, assigned_vehicle_number,
        team_leader_opinion, team_leader_review_time, engineering_dept_opinion, engineering_dept_review_time,
        actual_start_time, actual_end_time, actual_duration,
        f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id,
        f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id
    </sql>


</mapper>
