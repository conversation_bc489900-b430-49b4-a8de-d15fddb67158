package jnpf.vehicle.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import jnpf.vehicle.enums.VehicleApplicationStatusEnum;
import jnpf.vehicle.enums.VehicleApplicationPriorityEnum;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用车申请表
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("zz_vehicle_application")
public class VehicleApplicationEntity extends SuperEntity<String> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 申请单号（系统自动生成）
     */
    @NotNull(message = "申请单号不能为空")
    @SqlSearch
    @ExcelProperty("申请单号")
    @TableField("application_no")
    private String applicationNo;

    /**
     * 申请人ID
     */
    @NotNull(message = "申请人不能为空")
    @ExcelProperty("申请人ID")
    @TableField("applicant_id")
    private String applicantId;

    /**
     * 申请人姓名
     */
    @ExcelProperty("申请人")
    @TableField("applicant_name")
    private String applicantName;

    /**
     * 申请部门/队部
     */
    @NotNull(message = "申请部门不能为空")
    @ExcelProperty("申请部门/队部")
    @TableField("apply_department")
    private String applyDepartment;

    /**
     * 用车日期
     */
    @NotNull(message = "用车日期不能为空")
    @ExcelProperty("用车日期")
    @TableField("use_date")
    private Date useDate;

    /**
     * 预计用车时长（小时）
     */
    @ExcelProperty("预计用车时长")
    @TableField("estimated_duration")
    private BigDecimal estimatedDuration;

    /**
     * 用车目的
     */
    @NotNull(message = "用车目的不能为空")
    @ExcelProperty("用车目的")
    @TableField("use_purpose")
    private String usePurpose;

    /**
     * 优先级：1-一般，2-紧急
     */
    @ExcelProperty("优先级")
    @TableField("priority")
    private VehicleApplicationPriorityEnum priority;

    /**
     * 申请状态：1-待审核，2-已审核，3-已分配，4-使用中，5-已完成，6-已拒绝
     */
    @ExcelProperty("申请状态")
    @TableField("status")
    private VehicleApplicationStatusEnum status;

    /**
     * 分配车辆ID
     */
    @ExcelProperty("分配车辆ID")
    @TableField("assigned_vehicle_id")
    private String assignedVehicleId;

    /**
     * 分配车辆车牌号
     */
    @ExcelProperty("分配车辆")
    @TableField("assigned_vehicle_number")
    private String assignedVehicleNumber;

    /**
     * 队长审核意见
     */
    @ExcelProperty("队长审核意见")
    @TableField("team_leader_opinion")
    private String teamLeaderOpinion;

    /**
     * 队长审核时间
     */
    @ExcelProperty("队长审核时间")
    @TableField("team_leader_review_time")
    private Date teamLeaderReviewTime;

    /**
     * 工程部审核意见
     */
    @ExcelProperty("工程部审核意见")
    @TableField("engineering_dept_opinion")
    private String engineeringDeptOpinion;

    /**
     * 工程部审核时间
     */
    @ExcelProperty("工程部审核时间")
    @TableField("engineering_dept_review_time")
    private Date engineeringDeptReviewTime;

    /**
     * 实际使用开始时间
     */
    @ExcelProperty("实际使用开始时间")
    @TableField("actual_start_time")
    private Date actualStartTime;

    /**
     * 实际使用结束时间
     */
    @ExcelProperty("实际使用结束时间")
    @TableField("actual_end_time")
    private Date actualEndTime;

    /**
     * 实际使用时长（台班）
     */
    @ExcelProperty("实际使用时长")
    @TableField("actual_duration")
    private BigDecimal actualDuration;

    /**
     * 状态名称（用于显示）
     */
    @TableField(exist = false)
    @ExcelProperty("状态名称")
    private String statusName;

    /**
     * 优先级名称（用于显示）
     */
    @TableField(exist = false)
    @ExcelProperty("优先级名称")
    private String priorityName;

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    public String getStatusName() {
        return status != null ? status.getDesc() : null;
    }

    /**
     * 获取优先级名称
     *
     * @return 优先级名称
     */
    public String getPriorityName() {
        return priority != null ? priority.getDesc() : null;
    }
}
