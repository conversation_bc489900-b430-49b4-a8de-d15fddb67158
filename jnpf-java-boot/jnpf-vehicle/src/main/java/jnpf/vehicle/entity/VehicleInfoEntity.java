package jnpf.vehicle.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import jnpf.vehicle.enums.VehicleStatusEnum;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 车辆信息表
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("zz_vehicle_info")
public class VehicleInfoEntity extends SuperEntity<String> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 车牌号
     */
    @NotNull(message = "车牌号不能为空")
    @SqlSearch
    @ExcelProperty("车牌号")
    @TableField("vehicle_number")
    private String vehicleNumber;

    /**
     * 车辆型号
     */
    @ExcelProperty("车辆型号")
    @TableField("vehicle_model")
    private String vehicleModel;

    /**
     * 车辆使用单位
     */
    @ExcelProperty("车辆使用单位")
    @TableField("vehicle_use_unit")
    private String vehicleUseUnit;

    /**
     * 吨位
     */
    @ExcelProperty("吨位")
    @TableField("tonnage")
    private BigDecimal tonnage;

    /**
     * 使用状态：1-可用，2-维修中，3-报废
     */
    @ExcelProperty("使用状态")
    @TableField("status")
    private VehicleStatusEnum status;

    /**
     * 备注信息
     */
    @ExcelProperty("备注信息")
    @TableField("remark")
    private String remark;

    /**
     * 状态名称（用于显示）
     */
    @TableField(exist = false)
    @ExcelProperty("状态名称")
    private String statusName;

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    public String getStatusName() {
        return status != null ? status.getDesc() : null;
    }
}
