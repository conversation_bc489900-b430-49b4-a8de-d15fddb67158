package jnpf.vehicle.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆信息导入错误VO
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VehicleInfoEntityErrorVO extends VehicleInfoEntity {

    /**
     * 异常原因
     */
    @Excel(name = "异常原因", orderNum = "999")
    @JSONField(name = "errorsInfo")
    private String errorsInfo;
}
