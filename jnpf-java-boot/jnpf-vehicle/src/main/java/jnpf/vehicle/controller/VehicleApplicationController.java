package jnpf.vehicle.controller;

import jnpf.vehicle.entity.VehicleApplicationEntity;
import jnpf.vehicle.service.VehicleApplicationBiz;
import jnpf.base.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用车申请管理
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@RestController
@RequestMapping("/api/base/vehicle/application")
public class VehicleApplicationController extends BaseController<VehicleApplicationBiz, VehicleApplicationEntity> {


}
