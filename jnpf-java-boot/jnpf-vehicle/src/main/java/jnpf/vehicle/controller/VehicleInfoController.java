package jnpf.vehicle.controller;

import jnpf.vehicle.entity.VehicleInfoEntity;
import jnpf.vehicle.entity.VehicleInfoEntityErrorVO;
import jnpf.vehicle.service.VehicleInfoBiz;
import jnpf.base.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.model.FaImportReqVo;
import jnpf.model.FaImportResultVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 车辆信息管理
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@RestController
@RequestMapping("/api/base/vehicle/info")
public class VehicleInfoController extends BaseController<VehicleInfoBiz, VehicleInfoEntity> {

    /**
     * 导入预览
     */
    @Operation(summary = "导入预览")
    @GetMapping("/ImportPreview")
    public ActionResult<List<VehicleInfoEntityErrorVO>> importPreview(@RequestParam("fileId") String fileId) {
        return baseBiz.importPreview(fileId);
    }

    /**
     * 校验导入数据
     */
    @Operation(summary = "校验导入数据")
    @PostMapping("/validateImportData")
    public ActionResult<List<VehicleInfoEntityErrorVO>> validateImportData(@RequestBody FaImportReqVo<VehicleInfoEntity> params) {
        List<VehicleInfoEntity> dataList = params.getList();
        return baseBiz.validateImportData(dataList);
    }

    /**
     * 导入数据
     */
    @Operation(summary = "导入数据")
    @PostMapping("/importData")
    public ActionResult<FaImportResultVo<VehicleInfoEntity>> importData(@RequestBody FaImportReqVo<VehicleInfoEntity> params) {
        List<VehicleInfoEntity> list = params.getList();
        FaImportResultVo<VehicleInfoEntity> result = baseBiz.importData(list);
        return ActionResult.success(result);
    }

    /**
     * 统计各状态车辆数量
     */
    @Operation(summary = "统计各状态车辆数量")
    @GetMapping("/statusStatistics")
    public ActionResult<List<Map<String, Object>>> getStatusStatistics() {
        List<Map<String, Object>> statistics = baseBiz.getStatusStatistics();
        return ActionResult.success(statistics);
    }

    /**
     * 检查车牌号是否已存在
     */
    @Operation(summary = "检查车牌号是否已存在")
    @GetMapping("/checkVehicleNumber")
    public ActionResult<Boolean> checkVehicleNumber(@RequestParam("vehicleNumber") String vehicleNumber,
                                                     @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = baseBiz.isVehicleNumberExists(vehicleNumber, excludeId);
        return ActionResult.success(exists);
    }
}
