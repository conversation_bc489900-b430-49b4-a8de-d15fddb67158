package jnpf.vehicle.mapper;

import jnpf.vehicle.entity.VehicleInfoEntity;
import jnpf.base.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 车辆信息表 Mapper接口
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@Mapper
public interface VehicleInfoMapper extends SuperMapper<VehicleInfoEntity> {

    /**
     * 根据车牌号查询车辆信息（用于唯一性检查）
     *
     * @param vehicleNumber 车牌号
     * @return 车辆信息
     */
    VehicleInfoEntity getByVehicleNumber(@Param("vehicleNumber") String vehicleNumber);

    /**
     * 统计各状态车辆数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getStatusStatistics();
}
