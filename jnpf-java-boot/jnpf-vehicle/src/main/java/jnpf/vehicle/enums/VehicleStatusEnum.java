package jnpf.vehicle.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 车辆使用状态枚举
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@Getter
public enum VehicleStatusEnum implements IEnum<String> {

    /**
     * 可用
     */
    AVAILABLE("1", "可用"),

    /**
     * 维修中
     */
    MAINTENANCE("2", "维修中"),

    /**
     * 报废
     */
    SCRAPPED("3", "报废");

    @JsonValue
    @EnumValue
    private final String value;
    private final String desc;

    VehicleStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
