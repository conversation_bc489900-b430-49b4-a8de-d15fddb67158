package jnpf.vehicle.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 用车申请优先级枚举
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@Getter
public enum VehicleApplicationPriorityEnum implements IEnum<String> {

    /**
     * 一般
     */
    NORMAL("1", "一般"),

    /**
     * 紧急
     */
    URGENT("2", "紧急");

    @JsonValue
    @EnumValue
    private final String value;
    private final String desc;

    VehicleApplicationPriorityEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
