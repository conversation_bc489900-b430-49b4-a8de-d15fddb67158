package jnpf.vehicle.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 用车申请状态枚举
 *
 * <AUTHOR>
 * @version V3.5
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2024-12-22
 */
@Getter
public enum VehicleApplicationStatusEnum implements IEnum<String> {

    /**
     * 待审核
     */
    PENDING_REVIEW("1", "待审核"),

    /**
     * 已审核
     */
    REVIEWED("2", "已审核"),

    /**
     * 已分配
     */
    ASSIGNED("3", "已分配"),

    /**
     * 使用中
     */
    IN_USE("4", "使用中"),

    /**
     * 已完成
     */
    COMPLETED("5", "已完成"),

    /**
     * 已拒绝
     */
    REJECTED("6", "已拒绝");

    @JsonValue
    @EnumValue
    private final String value;
    private final String desc;

    VehicleApplicationStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
