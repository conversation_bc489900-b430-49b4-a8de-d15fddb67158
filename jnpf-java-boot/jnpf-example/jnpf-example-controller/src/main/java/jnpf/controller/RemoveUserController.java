package jnpf.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jnpf.base.ActionResult;
import jnpf.base.UserInfo;
import jnpf.base.controller.BaseController;
import jnpf.base.vo.DownloadVO;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.entity.RemoveUserEntity;
import jnpf.model.FaImportReqVo;
import jnpf.model.FaImportResultVo;
import jnpf.model.removeuser.*;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.service.UserService;
import jnpf.service.RemoveUserService;
import jnpf.service.impl.MasterUserServiceImpl;
import jnpf.util.GeneraterSwapUtil;
import jnpf.util.JsonUtil;
import jnpf.util.StringUtil;
import jnpf.util.UserProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Tag(name = "remove_user", description = "example")
@RequestMapping("/api/example/RemoveUser")
public class RemoveUserController extends BaseController<MasterUserServiceImpl, MasterUserExcelVO> {

    @Autowired UserService userService;
    @Autowired GeneraterSwapUtil generaterSwapUtil;
    @Autowired UserProvider userProvider;
    @Autowired RemoveUserService removeUserService;


    /**
     * 人员总清单列表
     *
     * @param removeUserPagination
     * @return
     */
    @Operation(summary = "人员总清单列表")
    @PostMapping("/getMasterList")
    public ActionResult getMasterList(@RequestBody RemoveUserPagination removeUserPagination) throws IOException {
        List<RemoveUserEntity> list = removeUserService.getMasterList(removeUserPagination);
        List<Map<String, Object>> realList = new ArrayList<>();
        for (RemoveUserEntity entity : list) {
            Map<String, Object> removeUserMap = JsonUtil.entityToMap(entity);
            removeUserMap.put("id", removeUserMap.get("id"));
            //副表数据
            //子表数据
            realList.add(removeUserMap);
        }
        //数据转换
        realList = generaterSwapUtil.swapDataList(realList, RemoveUserConstant.getFormData(), RemoveUserConstant.getColumnData(), removeUserPagination.getModuleId(), false);
        removeUserService.convertOrganize(realList);
        //返回对象
        PageListVO vo = new PageListVO();
        vo.setList(realList);
        PaginationVO page = JsonUtil.getJsonToBean(removeUserPagination, PaginationVO.class);
        page.setTotal((int) removeUserPagination.getTotal());
        vo.setPagination(page);
        return ActionResult.success(vo);
    }

    /**
     * 人员总清单列表 - 优化版本
     *
     * @param removeUserPagination
     * @return
     */
    @Operation(summary = "人员总清单列表V2")
    @PostMapping("/getMasterListV2")
    public ActionResult getMasterListV2(@RequestBody RemoveUserPagination removeUserPagination) {
        try {
            // 参数校验
            if (removeUserPagination == null) {
                return ActionResult.fail("请求参数不能为空");
            }
            
            // 调用优化后的服务方法
            PageListVO<RemoveUserEntity> result = removeUserService.getMasterListV2(removeUserPagination);
            return ActionResult.success(result);
            
        } catch (Exception e) {
            log.error("获取人员总清单失败", e);
            return ActionResult.fail("获取人员总清单失败: " + e.getMessage());
        }
    }

    /**
     * 撤场列表
     *
     * @param removeUserPagination
     * @return
     */
    @Operation(summary = "获取撤场列表")
    @PostMapping("/getRemoveList")
    public ActionResult getRemoveList(@RequestBody RemoveUserPagination removeUserPagination) throws IOException {
        List<RemoveUserEntity> list = removeUserService.getRemoveList(removeUserPagination);
        List<Map<String, Object>> realList = new ArrayList<>();
        for (RemoveUserEntity entity : list) {
            Map<String, Object> removeUserMap = JsonUtil.entityToMap(entity);
            removeUserMap.put("id", removeUserMap.get("id"));
            //副表数据
            //子表数据
            realList.add(removeUserMap);
        }
        //数据转换
        realList = generaterSwapUtil.swapDataList(realList, RemoveUserConstant.getFormData(), RemoveUserConstant.getColumnData(), removeUserPagination.getModuleId(), false);

        //返回对象
        PageListVO vo = new PageListVO();
        vo.setList(realList);
        PaginationVO page = JsonUtil.getJsonToBean(removeUserPagination, PaginationVO.class);
        page.setTotal((int) removeUserPagination.getTotal());
        vo.setPagination(page);
        return ActionResult.success(vo);
    }

    /**
     * 变更列表
     *
     * @param removeUserPagination
     * @return
     */
    @Operation(summary = "获取变更列表")
    @PostMapping("/getChangeList")
    public ActionResult getChangeList(@RequestBody RemoveUserPagination removeUserPagination) throws IOException {
        List<RemoveUserEntity> list = removeUserService.getChangeList(removeUserPagination);
        List<Map<String, Object>> realList = new ArrayList<>();
        for (RemoveUserEntity entity : list) {
            Map<String, Object> removeUserMap = JsonUtil.entityToMap(entity);
            removeUserMap.put("id", removeUserMap.get("id"));
            //副表数据
            //子表数据
            realList.add(removeUserMap);
        }
        //数据转换
        realList = generaterSwapUtil.swapDataList(realList, RemoveUserConstant.getFormData(), RemoveUserConstant.getColumnData(), removeUserPagination.getModuleId(), false);

        //返回对象
        PageListVO vo = new PageListVO();
        vo.setList(realList);
        PaginationVO page = JsonUtil.getJsonToBean(removeUserPagination, PaginationVO.class);
        page.setTotal((int) removeUserPagination.getTotal());
        vo.setPagination(page);
        return ActionResult.success(vo);
    }


    /**
     * 编辑
     *
     * @param id
     * @param removeUserForm
     * @return
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid RemoveUserForm removeUserForm,
                               @RequestParam(value = "isImport", required = false) boolean isImport) {
        removeUserForm.setId(id);
        if (!isImport) {
            String b = removeUserService.checkForm(removeUserForm, 1);
            if (StringUtil.isNotEmpty(b)) {
                return ActionResult.fail(b);
            }
        }
        RemoveUserEntity entity = removeUserService.getInfo(id);
        if (entity != null) {
            try {
                removeUserService.saveOrUpdate(removeUserForm, id, false);
            } catch (Exception e) {
                System.out.println(e.getMessage());
                return ActionResult.fail("修改数据失败");
            }
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 恢复
     *
     * @param id
     * @return
     */
    @Operation(summary = "恢复")
    @GetMapping("/recover/{id}")
    @Transactional
    public ActionResult recover(@PathVariable("id") String id) {
        removeUserService.recover(1, id);
        return ActionResult.success("恢复成功");
    }

    /**
     * 撤场提交
     *
     * @param id
     * @return
     */
    @Operation(summary = "撤场提交")
    @GetMapping("/change/{id}")
    @Transactional
    public ActionResult change(@PathVariable("id") String id) {
        removeUserService.change(2, id);
        return ActionResult.success("撤场申请已提交");
    }

    /**
     * 撤场审批
     *
     * @param id
     * @return
     */
    @Operation(summary = "撤场")
    @GetMapping("/remove/{id}")
    @Transactional
    public ActionResult remove(@PathVariable("id") String id) {
        removeUserService.remove(3, id);
        return ActionResult.success("撤场成功");
    }

    /**
     * 批量恢复
     *
     * @param ids
     * @return
     */
    @PostMapping("/batchRecover")
    @Transactional
    @Operation(summary = "批量恢复")
    public ActionResult batchRecover(@RequestBody String ids) {
        List<String> idList = JsonUtil.getJsonToList(ids, String.class);
        int i = 0;
        for (String allId : idList) {
            this.recover(allId);
            i++;
        }
        if (i == 0) {
            return ActionResult.fail("恢复失败");
        }
        return ActionResult.success("恢复成功");
    }

    /**
     * 批量撤场
     *
     * @param ids
     * @return
     */
    @PostMapping("/batchRemove")
    @Transactional
    @Operation(summary = "批量撤场")
    public ActionResult batchRemove(@RequestBody String ids) {
        List<String> idList = JsonUtil.getJsonToList(ids, String.class);
        int i = 0;
        for (String allId : idList) {
            this.remove(allId);
            i++;
        }
        if (i == 0) {
            return ActionResult.fail("撤场失败");
        }
        return ActionResult.success("撤场成功");
    }

    /**
     * 表单信息(详情页)
     * 详情页面使用-转换数据
     *
     * @param id
     * @return
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult detailInfo(@PathVariable("id") String id) {
        RemoveUserEntity entity = removeUserService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("表单数据不存在！");
        }
        Map<String, Object> removeUserMap = JsonUtil.entityToMap(entity);
        removeUserMap.put("id", removeUserMap.get("id"));
        //副表数据
        //子表数据
        removeUserMap = generaterSwapUtil.swapDataDetail(removeUserMap, RemoveUserConstant.getFormData(), "570589747513171909", false);
        return ActionResult.success(removeUserMap);
    }

    /**
     * 获取详情(编辑页)
     * 编辑页面使用-不转换数据
     *
     * @param id
     * @return
     */
    @Operation(summary = "信息")
    @GetMapping("/{id}")
    public ActionResult info(@PathVariable("id") String id) {
        RemoveUserEntity entity = removeUserService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("表单数据不存在！");
        }
        Map<String, Object> removeUserMap = JsonUtil.entityToMap(entity);
        removeUserMap.put("id", removeUserMap.get("id"));
        //副表数据
        //子表数据
        removeUserMap = generaterSwapUtil.swapDataForm(removeUserMap, RemoveUserConstant.getFormData(), RemoveUserConstant.TABLEFIELDKEY, RemoveUserConstant.TABLERENAMES);
        return ActionResult.success(removeUserMap);
    }

    /**
     * 导入预览
     */
    @Operation(summary = "导入预览")
    @GetMapping("/importUserInfoPreview")
    public ActionResult<List<MasterUserExcelErrorVO>> importUserInfoPreview(@RequestParam("fileId") String fileId) {
        return baseBiz.importPreview(fileId);
    }

    /**
     * 导入数据
     */
    @Operation(summary = "导入数据")
    @PostMapping("/ImportData")
    public ActionResult<FaImportResultVo<MasterUserExcelVO>> importData(@RequestBody FaImportReqVo<MasterUserExcelVO> params) throws Exception {
        List<MasterUserExcelVO> list = params.getList();
        FaImportResultVo<MasterUserExcelVO> result = baseBiz.importData(list);
        return ActionResult.success(result);
    }

    /**
     * 模板下载
     *
     * @return
     */
    @Operation(summary = "模板下载")
    @GetMapping("/templateDownload")
    public ActionResult<DownloadVO> TemplateDownload() {
        UserInfo userInfo = userProvider.get();
        DownloadVO vo = DownloadVO.builder().build();
        try {
            vo.setName("人员信息更新.xlsx");
            vo.setUrl("http://fa.file.dward.cn/zz_szh/pro/TemplateFile/%E4%BA%BA%E5%91%98%E4%BF%A1%E6%81%AF%E6%9B%B4%E6%96%B0.xlsx");
        } catch (Exception e) {
            log.error("信息导出Excel错误:" + e.getMessage());
        }
        return ActionResult.success(vo);
    }

    /**
     * 导入撤场预览
     */
    @Operation(summary = "导入撤场预览")
    @GetMapping("/removeImportPreview")
    public ActionResult<List<MasterUserExcelErrorVO>> removeImportPreview(@RequestParam("fileId") String fileId) {
        return baseBiz.importPreview(fileId);
    }

    /**
     * 导入数据
     */
    @Operation(summary = "导入撤场数据")
    @PostMapping("/removeImportData")
    public ActionResult<FaImportResultVo<MasterUserExcelVO>> removeImportData(@RequestBody FaImportReqVo<MasterUserExcelVO> params) throws Exception {
        List<MasterUserExcelVO> list = params.getList();
        FaImportResultVo<MasterUserExcelVO> result = baseBiz.removeImportData(list);
        return ActionResult.success(result);
    }

    /**
     * 流程会签单撤场
     */
    @Operation(summary = "流程会签单撤场")
    @PostMapping("/flow/countersignRemove")
    public ActionResult countersignRemove(@RequestBody RemoveUserFlow params) {
        removeUserService.changeByIdCard(params.getIdCard());
        return ActionResult.success();
    }

    /**
     * 退回恢复
     */
    @Operation(summary = "退回恢复")
    @PostMapping("/flow/backRecovery")
    public ActionResult backRecovery(@RequestBody RemoveUserFlow params) {
        removeUserService.recoveryByIdCard(params.getIdCard());
        return ActionResult.success();
    }

    /**
     * 获取文件
     */
    @Operation(summary = "获取文件")
    @GetMapping("/getFile/{id}")
    public ActionResult getFile(@PathVariable("id") String id) throws IOException {
        UserEntity info = userService.getInfo(id);
        return ActionResult.success(info);
    }

    /**
     * 上传提交文件
     */
    @Operation(summary = "上传提交文件")
    @PostMapping("/uploadSubmitFile")
    public ActionResult uploadSubmitFile(@RequestBody Map<String, Object> params) throws IOException {
        String id = MapUtil.getStr(params, "id");
        List<String> fileList = (List<String>) params.get("fileList");
        UserEntity userEntity = new UserEntity();
        userEntity.setInsuranceFile((String) params.get("insuranceFile"));
        userEntity.setContractFile((String) params.get("contractFile"));
        userEntity.setContractReceiptedFile((String) params.get("contractReceiptedFile"));
        userEntity.setPromiseFile((String) params.get("promiseFile"));
        userEntity.setRegistrationFile((String) params.get("registrationFile"));
        userEntity.setRequestFile((String) params.get("requestFile"));
        userEntity.setIdCardFile((String) params.get("idCardFile"));
        userEntity.setPhysicalExamFile((String) params.get("physicalExamFile"));
        userEntity.setFeedbackFile((String) params.get("feedbackFile"));
        userEntity.setId(id);
        userService.updateFile(id, fileList);
        userService.updateFileId(userEntity);
        return ActionResult.success("上传成功");
    }

    /**
     * 上传退场承诺书
     */
    @Operation(summary = "上传提交文件")
    @PostMapping("/uploadExitCommitment")
    public ActionResult uploadExitCommitment(@RequestBody Map<String, Object> params) throws IOException {
        String id = MapUtil.getStr(params, "id");
        String exitCommitmentId = (String) params.get("exitCommitment");
        UserEntity userEntity = new UserEntity();
        userEntity.setExitCommitment(exitCommitmentId);
        userEntity.setId(id);
        userService.updateExitCommitment(id, exitCommitmentId);
        userService.updateExitCommitmentId(userEntity);
        return ActionResult.success("上传成功");
    }

    /**
     * 优化的人员总清单分页查询
     * 使用Hutool工具类自动映射Map参数到Pagination对象，大幅简化代码
     */
    @Operation(summary = "人员总清单分页查询（优化版）")
    @PostMapping("/pageOptimized")
    public ActionResult<PageListVO<RemoveUserEntity>> pageOptimized(@RequestBody Map<String, Object> params) {
        try {
            // 使用Hutool工具类将Map参数直接转换为RemoveUserPagination对象
            RemoveUserPagination pagination = new RemoveUserPagination();
            BeanUtil.fillBeanWithMap(params, pagination, true);

            // 调用优化后的服务方法
            PageListVO<RemoveUserEntity> result = removeUserService.getMasterListV2(pagination);

            return ActionResult.success(result);

        } catch (Exception e) {
            log.error("分页查询人员总清单失败", e);
            return ActionResult.fail("分页查询失败: " + e.getMessage());
        }
    }

}
