package jnpf.service;

import jnpf.model.removeuser.*;
import jnpf.entity.*;
import java.util.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Param;
import jnpf.base.vo.PageListVO;

/**
 * remove_user
 * 版本： V3.5
 * 版权： xx有限公司（https://www.xx.com）
 * 作者： JNPF开发平台组
 * 日期： 2024-06-12
 */
public interface RemoveUserService extends IService<RemoveUserEntity> {
    List<RemoveUserEntity> getMasterList(RemoveUserPagination removeUserPagination);

    /**
     * 人员总清单列表 - 优化版本
     * @param removeUserPagination 分页查询参数
     * @return 分页结果
     */
    PageListVO<RemoveUserEntity> getMasterListV2(RemoveUserPagination removeUserPagination);

    List<Map<String, Object>> convertOrganize(List<Map<String, Object>> list);

    List<RemoveUserEntity> getRemoveList(RemoveUserPagination removeUserPagination);

    List<RemoveUserEntity> getChangeList(RemoveUserPagination removeUserPagination);

    List<RemoveUserEntity> getTypeList(RemoveUserPagination removeUserPagination,String dataType,Integer type);

    RemoveUserEntity getInfo(String id);

    void delete(RemoveUserEntity entity);

    void recover(Integer state, String id);

    void remove(Integer state, String id);

    void change(Integer state, String id);

    // 根据身份证变更
    void changeByIdCard(@Param("idCard") String idCard);

    // 退回恢复
    void recoveryByIdCard(@Param("idCard") String idCard);

    void create(RemoveUserEntity entity);

    boolean update(String id, RemoveUserEntity entity);

    //子表方法
    //副表数据方法
	String checkForm(RemoveUserForm form,int i);

    void saveOrUpdate(RemoveUserForm removeUserForm,String id, boolean isSave) throws Exception;

}
