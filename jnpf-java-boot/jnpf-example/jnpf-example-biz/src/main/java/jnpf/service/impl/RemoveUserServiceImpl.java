package jnpf.service.impl;

import jnpf.entity.*;
import jnpf.mapper.RemoveUserMapper;
import jnpf.permission.service.UserService;
import jnpf.permission.service.impl.OrganizeBiz;
import jnpf.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jnpf.model.removeuser.*;

import java.math.BigDecimal;

import cn.hutool.core.util.ObjectUtil;
import jnpf.permission.model.authorize.AuthorizeConditionModel;
import jnpf.util.GeneraterSwapUtil;
import jnpf.database.model.superQuery.SuperQueryJsonModel;
import jnpf.database.model.superQuery.ConditionJsonModel;
import jnpf.database.model.superQuery.SuperQueryConditionModel;

import java.lang.reflect.Field;

import com.baomidou.mybatisplus.annotation.TableField;

import java.util.regex.Pattern;

import jnpf.model.QueryModel;

import java.util.stream.Collectors;

import jnpf.base.model.ColumnDataModel;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jnpf.database.model.superQuery.SuperJsonModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.text.SimpleDateFormat;

import jnpf.util.*;

import java.util.*;

import jnpf.base.UserInfo;
import jnpf.permission.entity.UserEntity;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import java.util.stream.Collectors;

/**
 * remove_user
 * 版本： V3.5
 * 版权： xx有限公司（https://www.xx.com）
 * 作者： JNPF开发平台组
 * 日期： 2024-06-12
 */
@Service
public class RemoveUserServiceImpl extends ServiceImpl<RemoveUserMapper, RemoveUserEntity> implements RemoveUserService {

    public static final Integer MASTER_LIST = 1;
    public static final Integer CHANGE_LIST = 2;
    public static final Integer REMOVE_LIST = 3;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private RemoveUserMapper removeUserMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizeBiz organizeBiz;

    public void syncOldData(){
        lambdaUpdate()
            .set(RemoveUserEntity::getGlobalState,3 )
            .eq(RemoveUserEntity::getGlobalState,2)
            .update();

        lambdaUpdate()
            .set(RemoveUserEntity::getGlobalState,2 )
            .eq(RemoveUserEntity::getGlobalState,3)
            .update();
    }

    @Override
    public List<RemoveUserEntity> getMasterList(RemoveUserPagination removeUserPagination) {
//        ArrayList<RemoveUserEntity> userList = new ArrayList<>();
        List<RemoveUserEntity> typeList = getTypeList(removeUserPagination, removeUserPagination.getDataType(), MASTER_LIST);
//        for (int i = 0; i < typeList.size(); i++) {
//            if (typeList.get(i).getGlobalState().equals(1)) {
//                userList.add(typeList.get(i));
//            }
//        }
        return typeList;
    }

    @Override
    public List<Map<String, Object>> convertOrganize(List<Map<String, Object>> list){
        for (int i = 0; i < list.size(); i++) {
            String id = (String) list.get(i).get("id");
            UserEntity info = userService.getInfo(id);
            String organizeName = organizeBiz.getFullOrganizeName(info.getOrganizeId());
            list.get(i).put("organizeId",organizeName);
        }
        return list;
    }

    @Override
    public List<RemoveUserEntity> getRemoveList(RemoveUserPagination removeUserPagination) {
//        ArrayList<RemoveUserEntity> userList = new ArrayList<>();
        List<RemoveUserEntity> typeList = getTypeList(removeUserPagination, removeUserPagination.getDataType(), REMOVE_LIST);
//        for (int i = 0; i < typeList.size(); i++) {
//            if (typeList.get(i).getGlobalState().equals(2)) {
//                userList.add(typeList.get(i));
//            }
//        }
        return typeList;
    }

    @Override
    public List<RemoveUserEntity> getChangeList(RemoveUserPagination removeUserPagination) {
//        ArrayList<RemoveUserEntity> userList = new ArrayList<>();
        List<RemoveUserEntity> typeList = getTypeList(removeUserPagination, removeUserPagination.getDataType(), CHANGE_LIST);
//        for (int i = 0; i < typeList.size(); i++) {
//            if (typeList.get(i).getGlobalState().equals(3)) {
//                userList.add(typeList.get(i));
//            }
//        }
        return typeList;
    }

    /**
     * 人员总清单列表 - 优化版本
     * 优化点：
     * 1. 简化查询条件构建逻辑
     * 2. 减少不必要的数据转换
     * 3. 优化分页查询性能
     * 4. 统一异常处理
     */
    @Override
    public PageListVO<RemoveUserEntity> getMasterListV2(RemoveUserPagination removeUserPagination) {
        try {
            // 1. 构建基础查询条件
            QueryWrapper<RemoveUserEntity> queryWrapper = buildBaseQuery(removeUserPagination);
            
            // 2. 添加业务查询条件
            addBusinessConditions(queryWrapper, removeUserPagination);
            
            // 3. 添加权限过滤条件
            addPermissionFilter(queryWrapper, removeUserPagination);
            
            // 4. 设置排序
            addOrderBy(queryWrapper, removeUserPagination);
            
            // 5. 执行分页查询
            Page<RemoveUserEntity> page = new Page<>(
                removeUserPagination.getCurrentPage(), 
                removeUserPagination.getPageSize()
            );
            IPage<RemoveUserEntity> result = this.page(page, queryWrapper);
            
            // 6. 构建返回结果（直接使用实体列表，无需转换）
            return buildPageResultWithEntity(result.getRecords(), result.getTotal(), removeUserPagination);
            
        } catch (Exception e) {
            log.error("获取人员总清单失败", e);
            throw new RuntimeException("获取人员总清单失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建基础查询条件
     */
    private QueryWrapper<RemoveUserEntity> buildBaseQuery(RemoveUserPagination pagination) {
        QueryWrapper<RemoveUserEntity> queryWrapper = new QueryWrapper<>();
        
        // 固定条件：只查询人员总清单状态的数据
        queryWrapper.lambda().eq(RemoveUserEntity::getGlobalState, MASTER_LIST);
        
        // 启用状态过滤
        queryWrapper.lambda().eq(RemoveUserEntity::getEnabledMark, 1);
        
        return queryWrapper;
    }
    
    /**
     * 添加业务查询条件
     */
    private void addBusinessConditions(QueryWrapper<RemoveUserEntity> queryWrapper, RemoveUserPagination pagination) {
        // 入场状态
        if (ObjectUtil.isNotEmpty(pagination.getEntryMark())) {
            if ((int) pagination.getEntryMark() == 1) {
                queryWrapper.lambda().eq(RemoveUserEntity::getEntryMark, 1);
            } else {
                queryWrapper.lambda().isNull(RemoveUserEntity::getEntryMark);
            }
        }
        
        // 姓名模糊查询
        if (ObjectUtil.isNotEmpty(pagination.getRealName())) {
            queryWrapper.lambda().like(RemoveUserEntity::getRealName, pagination.getRealName());
        }
        
        // 来源单位
        if (ObjectUtil.isNotEmpty(pagination.getSourceUnit())) {
            queryWrapper.lambda().like(RemoveUserEntity::getSourceUnit, pagination.getSourceUnit());
        }
        
        // 身份证号
        if (ObjectUtil.isNotEmpty(pagination.getIdentificationNumber())) {
            queryWrapper.lambda().like(RemoveUserEntity::getIdentificationNumber, pagination.getIdentificationNumber());
        }
        
        // 手机号
        if (ObjectUtil.isNotEmpty(pagination.getMobilePhone())) {
            queryWrapper.lambda().like(RemoveUserEntity::getMobilePhone, pagination.getMobilePhone());
        }
        
        // 组织ID - 支持多选
        if (ObjectUtil.isNotEmpty(pagination.getOrganizeId())) {
            addMultiSelectCondition(queryWrapper, pagination.getOrganizeId(), RemoveUserEntity::getOrganizeId);
        }
        
        // 性别 - 支持多选
        if (ObjectUtil.isNotEmpty(pagination.getGender())) {
            addMultiSelectCondition(queryWrapper, pagination.getGender(), RemoveUserEntity::getGender);
        }
        
        // 职位ID - 支持多选
        if (ObjectUtil.isNotEmpty(pagination.getPositionId())) {
            addMultiSelectCondition(queryWrapper, pagination.getPositionId(), RemoveUserEntity::getPositionId);
        }
        
        // 团队 - 支持多选
        if (ObjectUtil.isNotEmpty(pagination.getTeam())) {
            addMultiSelectCondition(queryWrapper, pagination.getTeam(), RemoveUserEntity::getTeam);
        }
        
        // 民族 - 支持多选
        if (ObjectUtil.isNotEmpty(pagination.getNation())) {
            addMultiSelectCondition(queryWrapper, pagination.getNation(), RemoveUserEntity::getNation);
        }
        
        // 学历 - 支持多选
        if (ObjectUtil.isNotEmpty(pagination.getEducation())) {
            addMultiSelectCondition(queryWrapper, pagination.getEducation(), RemoveUserEntity::getEducation);
        }
        
        // 年龄
        if (ObjectUtil.isNotEmpty(pagination.getAge())) {
            queryWrapper.lambda().eq(RemoveUserEntity::getAge, pagination.getAge());
        }
        
        // 籍贯
        if (ObjectUtil.isNotEmpty(pagination.getNativePlace())) {
            queryWrapper.lambda().like(RemoveUserEntity::getNativePlace, pagination.getNativePlace());
        }
        
        // 人员类别
        if (ObjectUtil.isNotEmpty(pagination.getCategoryPersonnel())) {
            queryWrapper.lambda().like(RemoveUserEntity::getCategoryPersonnel, pagination.getCategoryPersonnel());
        }
        
        // 血型
        if (ObjectUtil.isNotEmpty(pagination.getBloodType())) {
            queryWrapper.lambda().like(RemoveUserEntity::getBloodType, pagination.getBloodType());
        }
        
        // 用工形式
        if (ObjectUtil.isNotEmpty(pagination.getFormEmployment())) {
            queryWrapper.lambda().like(RemoveUserEntity::getFormEmployment, pagination.getFormEmployment());
        }
        
        // 账户性质
        if (ObjectUtil.isNotEmpty(pagination.getNatureAccount())) {
            queryWrapper.lambda().like(RemoveUserEntity::getNatureAccount, pagination.getNatureAccount());
        }
        
        // 备注
        if (ObjectUtil.isNotEmpty(pagination.getRemark())) {
            queryWrapper.lambda().like(RemoveUserEntity::getRemark, pagination.getRemark());
        }
        
        // 时间范围查询
        addDateRangeConditions(queryWrapper, pagination);
    }
    
    /**
     * 添加多选条件的通用方法
     */
    private <T> void addMultiSelectCondition(QueryWrapper<RemoveUserEntity> queryWrapper, 
                                           Object value, 
                                           SFunction<RemoveUserEntity, T> column) {
        try {
            List<String> valueList = new ArrayList<>();
            
            if (value instanceof List) {
                valueList = (List<String>) value;
            } else if (value instanceof String) {
                String strValue = (String) value;
                if (strValue.startsWith("[") && strValue.endsWith("]")) {
                    valueList = JsonUtil.getJsonToList(strValue, String.class);
                } else {
                    valueList.add(strValue);
                }
            } else {
                valueList.add(String.valueOf(value));
            }
            
            if (!valueList.isEmpty()) {
                queryWrapper.lambda().in(column, valueList);
            }
        } catch (Exception e) {
            log.error("处理多选条件失败: {}", e);
            // 降级处理：使用like查询
            queryWrapper.lambda().like(column, String.valueOf(value));
        }
    }
    
    /**
     * 添加时间范围查询条件
     */
    private void addDateRangeConditions(QueryWrapper<RemoveUserEntity> queryWrapper, RemoveUserPagination pagination) {
        // 培训时间范围
        if (ObjectUtil.isNotEmpty(pagination.getTrainingTime())) {
            addDateRangeCondition(queryWrapper, pagination.getTrainingTime(), RemoveUserEntity::getTrainingTime);
        }
        
        // 生日范围
        if (ObjectUtil.isNotEmpty(pagination.getBirthday())) {
            addDateRangeCondition(queryWrapper, pagination.getBirthday(), RemoveUserEntity::getBirthday);
        }
        
        // 进项目时间范围
        if (ObjectUtil.isNotEmpty(pagination.getGoProjectTime())) {
            addDateRangeCondition(queryWrapper, pagination.getGoProjectTime(), RemoveUserEntity::getGoProjectTime);
        }
        
        // 撤场时间范围
        if (ObjectUtil.isNotEmpty(pagination.getRemoveTime())) {
            addDateRangeCondition(queryWrapper, pagination.getRemoveTime(), RemoveUserEntity::getRemoveTime);
        }

        // 毕业时间范围
        if (ObjectUtil.isNotEmpty(pagination.getGraduationTime())) {
            addDateRangeCondition(queryWrapper, pagination.getGraduationTime(), RemoveUserEntity::getGraduationTime);
        }
    }
    
    /**
     * 添加时间范围条件的通用方法
     */
    private void addDateRangeCondition(QueryWrapper<RemoveUserEntity> queryWrapper, 
                                     Object dateRange, 
                                     SFunction<RemoveUserEntity, Date> column) {
        try {
            List<String> dateList = JsonUtil.getJsonToList(String.valueOf(dateRange), String.class);
            if (dateList.size() >= 2) {
                Long startTime = Long.valueOf(dateList.get(0));
                Long endTime = Long.valueOf(dateList.get(1));
                
                Date startDate = new Date(startTime);
                Date endDate = DateUtil.stringToDate(DateUtil.daFormatYmd(endTime) + " 23:59:59");
                
                queryWrapper.lambda().ge(column, startDate).le(column, endDate);
            }
        } catch (Exception e) {
            log.error("处理多选条件失败: {}", e);
        }
    }
    
    /**
     * 添加权限过滤条件
     */
    private void addPermissionFilter(QueryWrapper<RemoveUserEntity> queryWrapper, RemoveUserPagination pagination) {
        // 如果不是管理员，需要添加数据权限过滤
        if (!userProvider.get().getIsAdministrator()) {
            // 这里可以根据具体的权限规则添加过滤条件
            // 例如：只能查看自己部门的数据
            String currentUserOrganizeId = userProvider.get().getOrganizeId();
            if (ObjectUtil.isNotEmpty(currentUserOrganizeId)) {
                queryWrapper.lambda().like(RemoveUserEntity::getOrganizeId, currentUserOrganizeId);
            }
        }
    }
    
    /**
     * 设置排序
     */
    private void addOrderBy(QueryWrapper<RemoveUserEntity> queryWrapper, RemoveUserPagination pagination) {
        if (ObjectUtil.isNotEmpty(pagination.getSidx())) {
            try {
                String sidx = pagination.getSidx();
                boolean isAsc = "asc".equalsIgnoreCase(pagination.getSort());
                
                // 根据字段名动态排序
                switch (sidx) {
                    case "realName":
                        queryWrapper.orderBy(true, isAsc, "F_REAL_NAME");
                        break;
                    case "sourceUnit":
                        queryWrapper.orderBy(true, isAsc, "SOURCE_UNIT");
                        break;
                    case "createTime":
                        queryWrapper.orderBy(true, isAsc, "F_CREATOR_TIME");
                        break;
                    default:
                        queryWrapper.lambda().orderByDesc(RemoveUserEntity::getId);
                        break;
                }
            } catch (Exception e) {
                log.error("处理多选条件失败: {}", e);
                queryWrapper.lambda().orderByDesc(RemoveUserEntity::getId);
            }
        } else {
            // 默认按ID倒序
            queryWrapper.lambda().orderByDesc(RemoveUserEntity::getId);
        }
    }
    
    /**
     * 构建分页结果（直接使用实体列表，无需转换）
     */
    private PageListVO<RemoveUserEntity> buildPageResultWithEntity(List<RemoveUserEntity> dataList, long total, RemoveUserPagination pagination) {
        PageListVO<RemoveUserEntity> result = new PageListVO<>();
        result.setList(dataList);
        
        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal((int) total);
        
        result.setPagination(paginationVO);
        return result;
    }

    /**
     * 列表查询
     */
    @Override
    public List<RemoveUserEntity> getTypeList(RemoveUserPagination removeUserPagination, String dataType, Integer type) {
        String userId = userProvider.get().getUserId();
        List<String> AllIdList = new ArrayList();
        List<List<String>> intersectionList = new ArrayList<>();
        boolean isPc = ServletUtil.getHeader("jnpf-origin").equals("pc");
        String columnData = !isPc ? RemoveUserConstant.getAppColumnData() : RemoveUserConstant.getColumnData();
        ColumnDataModel columnDataModel = JsonUtil.getJsonToBean(columnData, ColumnDataModel.class);
        String ruleJson = !isPc ? JsonUtil.getObjectToString(columnDataModel.getRuleListApp()) : JsonUtil.getObjectToString(columnDataModel.getRuleList());

        int total = 0;
        int removeUserNum = 0;
        QueryWrapper<RemoveUserEntity> removeUserQueryWrapper = new QueryWrapper<>();
        List<String> allSuperIDlist = new ArrayList<>();
        String superOp = "";
        if (ObjectUtil.isNotEmpty(removeUserPagination.getSuperQueryJson())) {
            List<String> allSuperList = new ArrayList<>();
            List<List<String>> intersectionSuperList = new ArrayList<>();
            String queryJson = removeUserPagination.getSuperQueryJson();
            SuperJsonModel superJsonModel = JsonUtil.getJsonToBean(queryJson, SuperJsonModel.class);
            int superNum = 0;
            QueryWrapper<RemoveUserEntity> removeUserSuperWrapper = new QueryWrapper<>();
            removeUserSuperWrapper = generaterSwapUtil.getCondition(new QueryModel(removeUserSuperWrapper, RemoveUserEntity.class, queryJson, "0"));
            int removeUserNum1 = removeUserSuperWrapper.getExpression().getNormal().size();
            if (removeUserNum1 > 0) {
                List<String> removeUserList = this.list(removeUserSuperWrapper).stream().map(RemoveUserEntity::getId).collect(Collectors.toList());
                allSuperList.addAll(removeUserList);
                intersectionSuperList.add(removeUserList);
                superNum++;
            }
            superOp = superNum > 0 ? superJsonModel.getMatchLogic() : "";
            //and or
            if (superOp.equalsIgnoreCase("and")) {
                allSuperIDlist = generaterSwapUtil.getIntersection(intersectionSuperList);
            } else {
                allSuperIDlist = allSuperList;
            }
        }
        List<String> allRuleIDlist = new ArrayList<>();
        List<Integer> allRuleGlobalStatelist = new ArrayList<>();
        String ruleOp = "";
        if (ObjectUtil.isNotEmpty(ruleJson)) {
            List<String> allRuleList = new ArrayList<>();
            List<List<String>> intersectionRuleList = new ArrayList<>();
            SuperJsonModel ruleJsonModel = JsonUtil.getJsonToBean(ruleJson, SuperJsonModel.class);
            int ruleNum = 0;
            QueryWrapper<RemoveUserEntity> removeUserSuperWrapper = new QueryWrapper<>();
            removeUserSuperWrapper = generaterSwapUtil.getCondition(new QueryModel(removeUserSuperWrapper, RemoveUserEntity.class, ruleJson, "0"));
            int removeUserNum1 = removeUserSuperWrapper.getExpression().getNormal().size();
            if (removeUserNum1 > 0) {
                List<String> removeUserList = this.list(removeUserSuperWrapper).stream().map(RemoveUserEntity::getId).collect(Collectors.toList());
                allRuleList.addAll(removeUserList);
                intersectionRuleList.add(removeUserList);
                ruleNum++;
            }
            ruleOp = ruleNum > 0 ? ruleJsonModel.getMatchLogic() : "";
            //and or
            if (ruleOp.equalsIgnoreCase("and")) {
                allRuleIDlist = generaterSwapUtil.getIntersection(intersectionRuleList);
            } else {
                allRuleIDlist = allRuleList;
            }
        }
        boolean pcPermission = false;
        boolean appPermission = false;
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object removeUserObj = generaterSwapUtil.getAuthorizeCondition(new QueryModel(removeUserQueryWrapper, RemoveUserEntity.class, removeUserPagination.getMenuId(), "0"));
                if (ObjectUtil.isEmpty(removeUserObj)) {
                    return new ArrayList<>();
                } else {
                    removeUserQueryWrapper = (QueryWrapper<RemoveUserEntity>) removeUserObj;
                    if (removeUserQueryWrapper.getExpression().getNormal().size() > 0) {
                        removeUserNum++;
                    }
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object removeUserObj = generaterSwapUtil.getAuthorizeCondition(new QueryModel(removeUserQueryWrapper, RemoveUserEntity.class, removeUserPagination.getMenuId(), "0"));
                if (ObjectUtil.isEmpty(removeUserObj)) {
                    return new ArrayList<>();
                } else {
                    removeUserQueryWrapper = (QueryWrapper<RemoveUserEntity>) removeUserObj;
                    if (removeUserQueryWrapper.getExpression().getNormal().size() > 0) {
                        removeUserNum++;
                    }
                }


            }
        }
        if (isPc) {

            // 查询入场状态
            if (ObjectUtil.isNotEmpty(removeUserPagination.getEntryMark())) {
                Integer entryMark = (Integer) removeUserPagination.getEntryMark();
                if (entryMark != null) {
                    if (entryMark == 1) {
                        removeUserQueryWrapper.lambda().like(RemoveUserEntity::getEntryMark, 1);
                    } else if (entryMark == 3) {
                        removeUserQueryWrapper.lambda().like(RemoveUserEntity::getEntryMark, 3);
                    } else {
                        removeUserQueryWrapper.lambda().isNull(RemoveUserEntity::getEntryMark);
                    }
                }
            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getSourceUnit())) {
                removeUserNum++;

                String value = removeUserPagination.getSourceUnit() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getSourceUnit()) :
                    String.valueOf(removeUserPagination.getSourceUnit());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getSourceUnit, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getRealName())) {
                removeUserNum++;

                String value = removeUserPagination.getRealName() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getRealName()) :
                    String.valueOf(removeUserPagination.getRealName());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getRealName, value);
            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getOrganizeId())) {
                removeUserNum++;

                List<String> idList = new ArrayList<>();
                try {
                    String[][] organizeId = JsonUtil.getJsonToBean(removeUserPagination.getOrganizeId(), String[][].class);
                    for (int i = 0; i < organizeId.length; i++) {
                        if (organizeId[i].length > 0) {
                            idList.add(JsonUtil.getObjectToString(Arrays.asList(organizeId[i])));
                        }
                    }
                } catch (Exception e1) {
                    try {
                        List<String> organizeId = JsonUtil.getJsonToList(removeUserPagination.getOrganizeId(), String.class);
                        if (organizeId.size() > 0) {
                            idList.addAll(organizeId);
                        }
                    } catch (Exception e2) {
                        idList.add(String.valueOf(removeUserPagination.getOrganizeId()));
                    }
                }
                removeUserQueryWrapper.lambda().and(t -> {
                    idList.forEach(tt -> {
                        t.like(RemoveUserEntity::getOrganizeId, tt).or();
                    });
                });

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getGender())) {
                removeUserNum++;

                List<String> idList = new ArrayList<>();
                try {
                    String[][] gender = JsonUtil.getJsonToBean(removeUserPagination.getGender(), String[][].class);
                    for (int i = 0; i < gender.length; i++) {
                        if (gender[i].length > 0) {
                            idList.add(JsonUtil.getObjectToString(Arrays.asList(gender[i])));
                        }
                    }
                } catch (Exception e1) {
                    try {
                        List<String> gender = JsonUtil.getJsonToList(removeUserPagination.getGender(), String.class);
                        if (gender.size() > 0) {
                            idList.addAll(gender);
                        }
                    } catch (Exception e2) {
                        idList.add(String.valueOf(removeUserPagination.getGender()));
                    }
                }
                removeUserQueryWrapper.lambda().and(t -> {
                    idList.forEach(tt -> {
                        t.like(RemoveUserEntity::getGender, tt).or();
                    });
                });

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getPositionId())) {
                removeUserNum++;

                List<String> idList = new ArrayList<>();
                try {
                    String[][] positionId = JsonUtil.getJsonToBean(removeUserPagination.getPositionId(), String[][].class);
                    for (int i = 0; i < positionId.length; i++) {
                        if (positionId[i].length > 0) {
                            idList.add(JsonUtil.getObjectToString(Arrays.asList(positionId[i])));
                        }
                    }
                } catch (Exception e1) {
                    try {
                        List<String> positionId = JsonUtil.getJsonToList(removeUserPagination.getPositionId(), String.class);
                        if (positionId.size() > 0) {
                            idList.addAll(positionId);
                        }
                    } catch (Exception e2) {
                        idList.add(String.valueOf(removeUserPagination.getPositionId()));
                    }
                }
                removeUserQueryWrapper.lambda().and(t -> {
                    idList.forEach(tt -> {
                        t.like(RemoveUserEntity::getPositionId, tt).or();
                    });
                });

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getIdentificationNumber())) {
                removeUserNum++;

                String value = removeUserPagination.getIdentificationNumber() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getIdentificationNumber()) :
                    String.valueOf(removeUserPagination.getIdentificationNumber());
                removeUserQueryWrapper.lambda()
                    .like(RemoveUserEntity::getIdentificationNumber, value);
            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getMobilePhone())) {
                removeUserNum++;

                String value = removeUserPagination.getMobilePhone() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getMobilePhone()) :
                    String.valueOf(removeUserPagination.getMobilePhone());
                removeUserQueryWrapper.lambda()
                    .like(RemoveUserEntity::getMobilePhone, value);
            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getTrainingTime())) {
                removeUserNum++;

                List TrainingTimeList = JsonUtil.getJsonToList(removeUserPagination.getTrainingTime(), String.class);
                Long fir = Long.valueOf(String.valueOf(TrainingTimeList.get(0)));
                Long sec = Long.valueOf(String.valueOf(TrainingTimeList.get(1)));

                removeUserQueryWrapper.lambda().ge(RemoveUserEntity::getTrainingTime, new Date(fir))
                    .le(RemoveUserEntity::getTrainingTime, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));


            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getBirthday())) {
                removeUserNum++;

                List BirthdayList = JsonUtil.getJsonToList(removeUserPagination.getBirthday(), String.class);
                Long fir = Long.valueOf(String.valueOf(BirthdayList.get(0)));
                Long sec = Long.valueOf(String.valueOf(BirthdayList.get(1)));

                removeUserQueryWrapper.lambda().ge(RemoveUserEntity::getBirthday, new Date(fir))
                    .le(RemoveUserEntity::getBirthday, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));


            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getTeam())) {
                removeUserNum++;

                List<String> idList = new ArrayList<>();
                try {
                    String[][] team = JsonUtil.getJsonToBean(removeUserPagination.getTeam(), String[][].class);
                    for (int i = 0; i < team.length; i++) {
                        if (team[i].length > 0) {
                            idList.add(JsonUtil.getObjectToString(Arrays.asList(team[i])));
                        }
                    }
                } catch (Exception e1) {
                    try {
                        List<String> team = JsonUtil.getJsonToList(removeUserPagination.getTeam(), String.class);
                        if (team.size() > 0) {
                            idList.addAll(team);
                        }
                    } catch (Exception e2) {
                        idList.add(String.valueOf(removeUserPagination.getTeam()));
                    }
                }
                removeUserQueryWrapper.lambda().and(t -> {
                    idList.forEach(tt -> {
                        t.like(RemoveUserEntity::getTeam, tt).or();
                    });
                });

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getAge())) {
                removeUserNum++;

                String value = removeUserPagination.getAge() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getAge()) :
                    String.valueOf(removeUserPagination.getAge());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getAge, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getNation())) {
                removeUserNum++;

                List<String> idList = new ArrayList<>();
                try {
                    String[][] nation = JsonUtil.getJsonToBean(removeUserPagination.getNation(), String[][].class);
                    for (int i = 0; i < nation.length; i++) {
                        if (nation[i].length > 0) {
                            idList.add(JsonUtil.getObjectToString(Arrays.asList(nation[i])));
                        }
                    }
                } catch (Exception e1) {
                    try {
                        List<String> nation = JsonUtil.getJsonToList(removeUserPagination.getNation(), String.class);
                        if (nation.size() > 0) {
                            idList.addAll(nation);
                        }
                    } catch (Exception e2) {
                        idList.add(String.valueOf(removeUserPagination.getNation()));
                    }
                }
                removeUserQueryWrapper.lambda().and(t -> {
                    idList.forEach(tt -> {
                        t.like(RemoveUserEntity::getNation, tt).or();
                    });
                });

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getEducation())) {
                removeUserNum++;

                List<String> idList = new ArrayList<>();
                try {
                    String[][] education = JsonUtil.getJsonToBean(removeUserPagination.getEducation(), String[][].class);
                    for (int i = 0; i < education.length; i++) {
                        if (education[i].length > 0) {
                            idList.add(JsonUtil.getObjectToString(Arrays.asList(education[i])));
                        }
                    }
                } catch (Exception e1) {
                    try {
                        List<String> education = JsonUtil.getJsonToList(removeUserPagination.getEducation(), String.class);
                        if (education.size() > 0) {
                            idList.addAll(education);
                        }
                    } catch (Exception e2) {
                        idList.add(String.valueOf(removeUserPagination.getEducation()));
                    }
                }
                removeUserQueryWrapper.lambda().and(t -> {
                    idList.forEach(tt -> {
                        t.like(RemoveUserEntity::getEducation, tt).or();
                    });
                });

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getNativePlace())) {
                removeUserNum++;

                String value = removeUserPagination.getNativePlace() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getNativePlace()) :
                    String.valueOf(removeUserPagination.getNativePlace());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getNativePlace, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getCategoryPersonnel())) {
                removeUserNum++;

                String value = removeUserPagination.getCategoryPersonnel() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getCategoryPersonnel()) :
                    String.valueOf(removeUserPagination.getCategoryPersonnel());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getCategoryPersonnel, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getGoProjectTime())) {
                removeUserNum++;

                List GoProjectTimeList = JsonUtil.getJsonToList(removeUserPagination.getGoProjectTime(), String.class);
                Long fir = Long.valueOf(String.valueOf(GoProjectTimeList.get(0)));
                Long sec = Long.valueOf(String.valueOf(GoProjectTimeList.get(1)));

                removeUserQueryWrapper.lambda().ge(RemoveUserEntity::getGoProjectTime, new Date(fir))
                    .le(RemoveUserEntity::getGoProjectTime, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));


            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getBloodType())) {
                removeUserNum++;

                String value = removeUserPagination.getBloodType() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getBloodType()) :
                    String.valueOf(removeUserPagination.getBloodType());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getBloodType, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getFormEmployment())) {
                removeUserNum++;

                String value = removeUserPagination.getFormEmployment() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getFormEmployment()) :
                    String.valueOf(removeUserPagination.getFormEmployment());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getFormEmployment, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getNatureAccount())) {
                removeUserNum++;

                String value = removeUserPagination.getNatureAccount() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getNatureAccount()) :
                    String.valueOf(removeUserPagination.getNatureAccount());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getNatureAccount, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getRemark())) {
                removeUserNum++;

                String value = removeUserPagination.getRemark() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getRemark()) :
                    String.valueOf(removeUserPagination.getRemark());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getRemark, value);

            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getRemoveTime())) {
                removeUserNum++;

                List RemoveTimeList = JsonUtil.getJsonToList(removeUserPagination.getRemoveTime(), String.class);
                Long fir = Long.valueOf(String.valueOf(RemoveTimeList.get(0)));
                Long sec = Long.valueOf(String.valueOf(RemoveTimeList.get(1)));

                removeUserQueryWrapper.lambda().ge(RemoveUserEntity::getRemoveTime, new Date(fir))
                    .le(RemoveUserEntity::getRemoveTime, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));


            }

            if (ObjectUtil.isNotEmpty(removeUserPagination.getRemoveRemark())) {
                removeUserNum++;

                String value = removeUserPagination.getRemoveRemark() instanceof List ?
                    JsonUtil.getObjectToString(removeUserPagination.getRemoveRemark()) :
                    String.valueOf(removeUserPagination.getRemoveRemark());
                removeUserQueryWrapper.lambda().like(RemoveUserEntity::getRemoveRemark, value);

            }

        }
        List<String> intersection = generaterSwapUtil.getIntersection(intersectionList);
        if (total > 0) {
            if (intersection.size() == 0) {
                intersection.add("jnpfNullList");
            }
            removeUserQueryWrapper.lambda().in(RemoveUserEntity::getId, intersection);
        }
        //是否有高级查询
        if (ObjectUtil.isNotEmpty(superOp)) {
            if (allSuperIDlist.size() == 0) {
                allSuperIDlist.add("jnpfNullList");
            }
            List<String> finalAllSuperIDlist = allSuperIDlist;
            removeUserQueryWrapper.lambda().and(t -> t.in(RemoveUserEntity::getId, finalAllSuperIDlist));
        }
        //是否有数据过滤查询
        if (ObjectUtil.isNotEmpty(ruleOp)) {
            if (allRuleIDlist.size() == 0) {
                allRuleIDlist.add("jnpfNullList");
            }
            List<String> finalAllRuleIDlist = allRuleIDlist;
            removeUserQueryWrapper.lambda().and(t -> t.in(RemoveUserEntity::getId, finalAllRuleIDlist));
        }

        if (allRuleGlobalStatelist.size() == 0) {
            allRuleGlobalStatelist.add(type);
        }
        List<Integer> finalAllRuleGlobalStatelist = allRuleGlobalStatelist;
        removeUserQueryWrapper.lambda().and(t -> t.in(RemoveUserEntity::getGlobalState, finalAllRuleGlobalStatelist));

        //排序
        if (ObjectUtil.isEmpty(removeUserPagination.getSidx())) {
            removeUserQueryWrapper.lambda().orderByDesc(RemoveUserEntity::getId);
        } else {
            try {
                String sidx = removeUserPagination.getSidx();
                String[] strs = sidx.split("_name");
                RemoveUserEntity removeUserEntity = new RemoveUserEntity();
                Field declaredField = removeUserEntity.getClass().getDeclaredField(strs[0]);
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                removeUserQueryWrapper = "asc".equals(removeUserPagination.getSort().toLowerCase()) ? removeUserQueryWrapper.orderByAsc(value) : removeUserQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }

        if ("0".equals(dataType)) {
            if ((total > 0 && AllIdList.size() > 0) || total == 0) {
                Page<RemoveUserEntity> page = new Page<>(removeUserPagination.getCurrentPage(), removeUserPagination.getPageSize());
                IPage<RemoveUserEntity> userIPage = this.page(page, removeUserQueryWrapper);
                return removeUserPagination.setData(userIPage.getRecords(), userIPage.getTotal());
            } else {
                List<RemoveUserEntity> list = new ArrayList();
                return removeUserPagination.setData(list, list.size());
            }
        } else {
            return this.list(removeUserQueryWrapper);
        }
    }

    @Override
    public RemoveUserEntity getInfo(String id) {
        QueryWrapper<RemoveUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RemoveUserEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(RemoveUserEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, RemoveUserEntity entity) {
        return this.updateById(entity);
    }

    @Override
    public void delete(RemoveUserEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }

    @Override
    public void recover(Integer state, String id) {
        removeUserMapper.recover(state, id);
    }

    @Override
    public void remove(Integer state, String id) {
        removeUserMapper.remove(state, id);
    }

    @Override
    public void change(Integer state, String id) {
        removeUserMapper.remove(state, id);
    }

    @Override
    public void changeByIdCard(String idCard) {
        removeUserMapper.changeByIdCard(idCard);
    }

    @Override
    public void recoveryByIdCard(String idCard) {
        lambdaUpdate()
            .set(RemoveUserEntity::getGlobalState, RemoveUserEntity.GlobalState.REMOVE)
            .eq(RemoveUserEntity::getCertificatesNumber, idCard)
            .update();
//        removeUserMapper.removeByIdCard(idCard);
    }

    /**
     * 验证表单唯一字段，正则，非空 i-0新增-1修改
     */
    @Override
    public String checkForm(RemoveUserForm form, int i) {
        boolean isUp = StringUtil.isNotEmpty(form.getId()) && !form.getId().equals("0");
        String id = "";
        String countRecover = "";
        if (isUp) {
            id = form.getId();
        }
        //主表字段验证
        if (StringUtil.isNotEmpty(form.getIdentificationNumber())) {
            if (!Pattern.compile("^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$").matcher(String.valueOf(form.getIdentificationNumber())).matches()) {
                return "请输入正确的身份证号码";
            }
        }
        if (StringUtil.isNotEmpty(form.getMobilePhone())) {
            if (!Pattern.compile("^1[3456789]\\d{9}$").matcher(String.valueOf(form.getMobilePhone())).matches()) {
                return "请输入正确的手机号码";
            }
        }
        return countRecover;
    }

    /**
     * 新增修改数据(事务回滚)
     *
     * @param id
     * @param removeUserForm
     * @return
     */
    @Override
    @Transactional
    public void saveOrUpdate(RemoveUserForm removeUserForm, String id, boolean isSave) throws Exception {
        UserInfo userInfo = userProvider.get();
        UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());
        removeUserForm = JsonUtil.getJsonToBean(
            generaterSwapUtil.swapDatetime(RemoveUserConstant.getFormData(), removeUserForm), RemoveUserForm.class);
        RemoveUserEntity entity = JsonUtil.getJsonToBean(removeUserForm, RemoveUserEntity.class);

        if (isSave) {
            String mainId = RandomUtil.uuId();
            entity.setId(mainId);
        } else {
        }
        UserEntity userRelation = new UserEntity();
        if (entity.getOrganizeId() != null){
            userRelation.setOrganizeId(entity.getOrganizeId());
        }else {
            userRelation.setOrganizeId(null);
        }
        if (entity.getPositionId() != null){
            userRelation.setPositionId(entity.getPositionId());
        }else {
            userRelation.setPositionId(null);
        }
        if (entity.getTeam() != null){
            userRelation.setTeam(entity.getTeam());
        }else {
            userRelation.setTeam(null);
        }
        userService.updateRelationAll(id, userRelation);
        this.saveOrUpdate(entity);

    }
}
