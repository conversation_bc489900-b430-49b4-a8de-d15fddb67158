package jnpf.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import jnpf.base.ActionResult;
import jnpf.base.service.DbLinkService;
import jnpf.database.model.entity.DbLinkEntity;
import jnpf.entity.FlowFormEntity;
import jnpf.exception.DataException;
import jnpf.exception.WorkFlowException;
import jnpf.model.visualJson.FormDataModel;
import jnpf.model.visualJson.TableModel;
import jnpf.permission.entity.UserEntity;
import jnpf.base.UserInfo;
import jnpf.permission.model.organize.OrganizeConditionModel;
import jnpf.permission.model.user.mod.UserConditionModel;
import jnpf.permission.service.OrganizeService;
import jnpf.permission.service.UserService;
import jnpf.service.FlowFormService;
import jnpf.service.FormDataService;
import jnpf.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.lang.reflect.Type;
import java.sql.SQLException;
import java.util.*;


@Service
public class FlowDataServiceImpl implements FormDataService {
	@Autowired
	private FlowFormCustomUtils flowFormCustomUtils;
	@Autowired
	private FlowFormHttpReqUtils flowFormHttpReqUtils;

	@Autowired
	private FlowFormService flowFormService;
	@Autowired
	private DbLinkService dblinkService;
	@Autowired
	private FlowFormDataUtil flowDataUtil;
	@Autowired
	private UserService userService;
	@Autowired
	private OrganizeService organizeService;
	@Autowired
	private UserProvider userProvider;

    @Override
    public void create(String formId, String id, Map<String, Object> map) throws WorkFlowException {
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        //判断是否为系统表单
        boolean b = flowFormEntity.getFormType() == 1;
        if (b) {
            flowFormHttpReqUtils.create(flowFormEntity, id, UserProvider.getToken(), map);
        } else {
            flowFormCustomUtils.create(flowFormEntity, id, map, null);
        }
    }

    @Override
    public void update(String formId, String id, Map<String, Object> map) throws WorkFlowException, SQLException, DataException {
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        //判断是否为系统表单
        boolean b = flowFormEntity.getFormType() == 1;
        if (b) {
            flowFormHttpReqUtils.update(flowFormEntity, id, UserProvider.getToken(), map);
        } else {
            flowFormCustomUtils.update(flowFormEntity, id, map);
        }
    }

    @Override
    public void saveOrUpdate(String formId, String id, Map<String, Object> map, UserEntity delegateUser) throws WorkFlowException {
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        Integer formType = flowFormEntity.getFormType();
        if(map.get(TableFeildsEnum.VERSION.getField().toUpperCase())!=null){//针对Oracle数据库大小写敏感，出现大写字段补充修复
            map.put(TableFeildsEnum.VERSION.getField(),map.get(TableFeildsEnum.VERSION.getField().toUpperCase()));
        }
        //系统表单
        if (formType == 1) {
            flowFormHttpReqUtils.saveOrUpdate(flowFormEntity, id, UserProvider.getToken(), map);
        } else {
            try {
                flowFormCustomUtils.saveOrUpdate(flowFormEntity, id, map, delegateUser);
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
            } catch (DataException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void saveOrUpdate(String formId, String id, Map<String, Object> map, UserEntity delegateUser, String thisStepId) throws WorkFlowException {
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        Integer formType = flowFormEntity.getFormType();
        if(map.get(TableFeildsEnum.VERSION.getField().toUpperCase())!=null){//针对Oracle数据库大小写敏感，出现大写字段补充修复
            map.put(TableFeildsEnum.VERSION.getField(),map.get(TableFeildsEnum.VERSION.getField().toUpperCase()));
        }
        //系统表单
        if (formType == 1) {
            flowFormHttpReqUtils.saveOrUpdate(flowFormEntity, id, UserProvider.getToken(), map);
        } else {
            try {
                flowFormCustomUtils.saveOrUpdate(flowFormEntity, id, map, delegateUser,thisStepId);
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
            } catch (DataException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean delete(String formId, String id) throws Exception {
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        List<TableModel> tableModels = JsonUtil.getJsonToList(flowFormEntity.getTableJson(), TableModel.class);
        FormDataModel formData = JsonUtil.getJsonToBean(flowFormEntity.getPropertyJson(), FormDataModel.class);
        Integer primaryKeyPolicy = formData.getPrimaryKeyPolicy();
        DbLinkEntity linkEntity = StringUtil.isNotEmpty(flowFormEntity.getDbLinkId()) ? dblinkService.getInfo(flowFormEntity.getDbLinkId()) : null;
        flowDataUtil.deleteTable(id, primaryKeyPolicy, tableModels, linkEntity);
        return true;
    }

    @Override
    public ActionResult info(String formId, String id){
        ActionResult result = new ActionResult();
        Map<String, Object> allDataMap = new HashMap();
        FlowFormEntity flowFormEntity = flowFormService.getById(formId);
        result.setCode(flowFormEntity==null?400:200);
        result.setMsg(flowFormEntity==null?"表单信息不存在":"");
        if(flowFormEntity!=null){
            //判断是否为系统表单
            boolean b = flowFormEntity.getFormType() == 1;
            if (b) {
                allDataMap.putAll(flowFormHttpReqUtils.info(flowFormEntity, id, UserProvider.getToken()));
            } else {
                allDataMap.putAll(flowFormCustomUtils.info(flowFormEntity, id));
            }
        }
        //档案附件格式转换
        if (allDataMap.containsKey("appendFile") && allDataMap.get("appendFile") != null) {
            Gson gson = new Gson();
            Type listType = new TypeToken<List<String>>(){}.getType();
            List<String> appendFiles = gson.fromJson(allDataMap.get("appendFile").toString(), listType);
            allDataMap.put("appendFile", appendFiles);
        }
        result.setData(allDataMap);
        return result;
    }
}
