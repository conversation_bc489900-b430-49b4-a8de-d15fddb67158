package jnpf.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jnpf.base.entity.SuperBaseEntity;
import jnpf.base.service.BaseBiz;
import jnpf.engine.entity.FlowTaskEntity;
import jnpf.engine.enums.FlowTaskStatusEnum;
import jnpf.engine.model.flowengine.FlowModel;
import jnpf.engine.util.FlowNature;
import jnpf.exception.DataException;
import jnpf.message.service.SmsSendService;
import jnpf.model.query.QueryParams;
import jnpf.permission.entity.PositionEntity;
import jnpf.permission.entity.RoleEntity;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.enums.user.UserCompontTypeEnum;
import jnpf.permission.mapper.UserMapper;
import jnpf.permission.model.user.form.UserSmsModifyPasswordForm;
import jnpf.permission.service.UserService;
import jnpf.service.StorageService;
import jnpf.util.StringUtil;
import jnpf.util.UserProvider;
import jnpf.utils.FaceUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserBiz extends BaseBiz<UserMapper, UserEntity> {

        @Autowired RoleBiz roleBiz;
        @Autowired PositionBiz positionBiz;
        @Autowired OrganizeBiz organizeBiz;
        @Autowired UserProvider userProvider;
        @Autowired UserService userService;
        @Autowired StorageService fileSaveBiz;
        @Autowired FaceUtils faceUtils;
        @Lazy @Autowired SmsSendService smsSendService;

        /**
         * 根据用户组件参数查询所有用户
         * @return
         */
        public List<String> selectByComponParam(List<String> params) {
            List<String> userIds = new ArrayList<>();

            params.forEach(
                param -> {
                    String[] splitArr = param.split("--");
                    UserCompontTypeEnum typeEnum = UserCompontTypeEnum.getByValue(splitArr[1]);
    //                System.out.println("typeEnum: " + typeEnum);

                    switch (typeEnum) {
                        case USER:
                        case DEPARTMENT:
                        case ROLE:
                        case POSITION:
                        case GROUP: {
                            HashMap<String, Object> queryMap = new HashMap<>();
                            queryMap.put(typeEnum.getTypeDesc(), splitArr[0]);
                            List<UserEntity> userList = this.list(new QueryParams(queryMap));

                            if (CollectionUtil.isNotEmpty(userList)) {
                                List<String> userIdsTemp = userList.stream()
                                    .map(userEntity -> {
                                        return userEntity.getId();
                                    })
                                    .collect(Collectors.toList());
                                userIds.addAll(userIdsTemp);
                            }
                        }
                        break;
                    }
                }
            );

            return userIds;
        }

        /**
         * 补全用户信息
         *
         * @param user
         */
        public void fulfill(UserEntity user) {
            if (user == null) return;
            if (StringUtil.isNotEmpty(user.getRoleId())) {
                RoleEntity role = roleBiz.getByIdWithCache(user.getRoleId());
                user.setRoleName(role != null ? role.getFullName() : "");
            }

            if (StringUtil.isNotEmpty(user.getPositionId())) {
                PositionEntity pos = positionBiz.getByIdWithCache(user.getPositionId());
                user.setPositionName(pos != null ? pos.getFullName() : "");
            }

            if (StrUtil.isNotEmpty(user.getSourceUnit())) {
                user.setSourceUnit(user.getSourceUnit().replace("核", ""));
            }

            String organizeName = organizeBiz.getFullOrganizeName(user.getOrganizeId());
            user.setOrganizeName(organizeName);
        }
        @SneakyThrows
        public void setName(Object o, String fieldName) {
            Object value = ReflectUtil.getFieldValue(o, fieldName);
            if (value == null) return;
            String id = value.toString();
            UserEntity user = this.getByIdWithCache(id);
            String name = (user != null ? user.getRealName() : "");
            ReflectUtil.setFieldValue(o, fieldName + "Name", name);
        }

        public List<UserEntity> getListByUserIds(List<String> ids) {
            // 达梦数据库无法null值入参
            ids.removeAll(Collections.singleton(null));
            if (CollUtil.isEmpty(ids)) {
                return Collections.emptyList();
            }
            List<UserEntity> list = this.lambdaQuery()
                .in(UserEntity::getId, ids)
                .ne(UserEntity::getEnabledMark, 0)
                .list();
            return list;
        }

        /**
         * 根据身份证查询数据
         */
        public UserEntity getByIdCard(String idCard) {
            return lambdaQuery().eq(UserEntity::getIdentificationNumber, idCard).one();
        }

        public UserEntity getByTel(String tel) {
            return lambdaQuery()
                .eq(UserEntity::getMobilePhone, tel)
                .one();
        }

        public void updateMyOfficialPhoto(String officialPhotoFileId) {
            String userId = userProvider.get().getUserId();

            File file = fileSaveBiz.getByFileId(officialPhotoFileId);
            // 检查照片中是否有人脸
            boolean hasFace = faceUtils.detect(file);
            if (!hasFace) {
                throw new DataException("照片中未检测到人脸，请重新选择照片");
            }

            lambdaUpdate()
                .eq(UserEntity::getId, userId)
                .set(UserEntity::getOfficialPhotoFileId, officialPhotoFileId)
                .update();
        }

        public void checkTelLegal(String tel) {
            long count = lambdaQuery().eq(UserEntity::getMobilePhone, tel).count();
            if (count == 0) {
                throw new DataException("手机号不存在，请联系管理员");
            }
            if (count > 1) {
                throw new DataException("该手机号绑定了多个账户，请联系管理员");
            }
        }

        public void updateMyPwdBySmsCode(UserSmsModifyPasswordForm form) {
            String tel = form.getAccount();
            checkTelLegal(tel);
            smsSendService.valid(tel, form.getCode());
            // change password
            UserEntity user = getByTel(tel);
            user.setPassword(form.getPassword());

            userService.updatePassword(user);
            userService.delCurUser(user.getId());
        }

        public void decorateCrt(SuperBaseEntity.SuperCBaseEntity entity) {
            if (ObjUtil.isEmpty(entity) || StrUtil.isEmpty(entity.getCreatorUserId())) return;
            UserEntity creator = this.getByIdWithCache(entity.getCreatorUserId());
            entity.setCreatorUserName(creator != null ? creator.getRealName() : "");
        }

        @SneakyThrows
        public void setIdByName(Object o, String fieldName, Class clazz) {
            String fullName = ReflectUtil.getFieldValue(o, fieldName + "Name").toString();
            List<UserEntity> areaList = this.lambdaQuery()
                .eq(UserEntity::getRealName, fullName)
                .list();

            Field field = ReflectUtil.getField(clazz, fieldName);
            String fileName = field.getAnnotation(ExcelProperty.class) != null ? field.getAnnotation(ExcelProperty.class).value()[0] : "";
            if (areaList.size() > 1) throw new DataException("导入字段" + fileName + "时，用户表有重名数据");

            UserEntity entity = areaList.get(0);
            String id = (entity != null ? entity.getId() : "");
            ReflectUtil.setFieldValue(o, fieldName, id);
        }

        /**
         * 根据用户手机号为账户查询
         * @param phone
         * @return
         */
        public UserEntity getByPhone(String phone) {
            long count = lambdaQuery().eq(UserEntity::getAccount, phone).count();
            if (count > 1) {
                throw new DataException("该手机号绑定了多个账户，请联系管理员");
            }
            if (count == 0) return null;
            return lambdaQuery().eq(UserEntity::getAccount, phone).one();
        }

        /**
         * 离场会签流程是否结束并同步入场状态
         * @param flowTask 流程模板模型
         * @param flowModel 流程模型（包含表单数据）
         */
        public void isLeaveSignProcessAndSync(FlowTaskEntity flowTask, FlowModel flowModel) {
            try {
                if (flowTask == null || flowModel == null || flowModel.getFormData() == null) {
                    return;
                }
                if (!"离场会签".equals(flowTask.getFlowName()) &&
                    flowTask.getCompletion() != 100 &&
                    !flowTask.getStatus().equals(FlowTaskStatusEnum.Adopt.getCode())) {
                    return;
                }
                //身份证号
                Map<String, Object> formData = flowModel.getFormData();
                Object userIdObj = formData.get("f_user_id");
                if (userIdObj == null) {
                    log.warn("离场会签流程表单中f_user_id为空");
                    return;
                }
                if (userIdObj.toString().trim().isEmpty()) {
                    return;
                }
                this.syncEntryMarkStatus(userIdObj.toString().trim());
            } catch (Exception e) {
                log.error("校验离场会签流程并同步状态时发生异常", e);
            }
        }


        /**
         * 根据身份证号同步用户入场状态为"已离场"
         */
        public void syncEntryMarkStatus(String identificationNumber) {
            try {
                if (identificationNumber == null || identificationNumber.trim().isEmpty()) {
                    log.info("同步入场状态失败：身份证号为空");
                    return;
                }
                UpdateWrapper<UserEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(UserEntity::getIdentificationNumber, identificationNumber.trim()).set(UserEntity::getEntryMark, 3);
                boolean updateResult = this.update(updateWrapper);
                if (updateResult) {
                    log.info("身份证号[{}]的用户入场状态已同步为已离场", identificationNumber);
                } else {
                    log.info("未找到身份证号为[{}]的用户，无需同步状态", identificationNumber);
                }
            } catch (Exception e) {
                log.error("同步用户入场状态时发生异常，身份证号：{}", identificationNumber, e);
            }
        }

}
