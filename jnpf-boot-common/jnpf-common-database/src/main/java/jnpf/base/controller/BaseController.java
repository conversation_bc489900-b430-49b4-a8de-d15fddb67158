package jnpf.base.controller;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.service.BaseBiz;
import jnpf.base.utils.BaseResHandler;
import jnpf.base.vo.PageListVO;
import jnpf.model.query.QueryParams;
import jnpf.util.UserProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <h3>通用Rest接口父类，包含基本的方法：</h3>
 *
 * <table>
 *     <thead><tr><td>方法</td><td>说明</td></tr></thead>
 *     <tbody>
 *         <tr><td>{@link BaseController#save}</td>                 <td>新增</td></tr>
 *         <tr><td>{@link BaseController#saveBatch}</td>            <td>新增批量</td></tr>
 *         <tr><td>{@link BaseController#getById}</td>              <td>id查询</td></tr>
 *         <tr><td>{@link BaseController#getDetailById}</td>            <td>id查询详情</td></tr>
 *         <tr><td>{@link BaseController#getByIds}</td>             <td>ids集合查询</td></tr>
 *         <tr><td>{@link BaseController#update}</td>               <td>更新</td></tr>
 *         <tr><td>{@link BaseController#updateBatch}</td>          <td>批量更新</td></tr>
 *         <tr><td>{@link BaseController#saveOrUpdate}</td>         <td>新增or更新</td></tr>
 *         <tr><td>{@link BaseController#saveOrUpdateBatch}</td>    <td>批量新增or更新</td></tr>
 *         <tr><td>{@link BaseController#remove}</td>               <td>id删除</td></tr>
 *         <tr><td>{@link BaseController#removeBatchByIds}</td>     <td>ids批量删除</td></tr>
 *         <tr><td>{@link BaseController#removePer}</td>            <td>id永久删除</td></tr>
 *         <tr><td>{@link BaseController#removePerBatchByIds}</td>  <td>ids批量永久删除</td></tr>
 *         <tr><td>{@link BaseController#removeByQuery}</td>        <td>通过查询条件删除</td></tr>
 *         <tr><td>{@link BaseController#all}</td>                  <td>获取所有List</td></tr>
 *         <tr><td>{@link BaseController#list}</td>                 <td>获取List，带过滤查询条件</td></tr>
 *         <tr><td>{@link BaseController#listByAllPara}</td>        <td>获取List，不过滤查询条件</td></tr>
 *         <tr><td>{@link BaseController#mineList}</td>             <td>获取List(限定登录用户创建)，带过滤查询条件</td></tr>
 *         <tr><td>{@link BaseController#count}</td>                <td>过滤条件统计数量</td></tr>
 *         <tr><td>{@link BaseController#page}</td>                 <td>分页获取</td></tr>
 *         <tr><td>{@link BaseController#minePage}</td>             <td>个人分页查询</td></tr>
 *         <tr><td>{@link BaseController#exportExcel}</td>          <td>过滤条件导出Excel</td></tr>
 *         <tr><td>{@link BaseController#exportTplExcel}</td>       <td>下载导入Excel模版</td></tr>
 *         <tr><td>{@link BaseController#importExcel}</td>          <td>导入Excel数据</td></tr>
 *     </tbody>
 * </table>
 *
 * @param <Biz>    {@link BaseBiz}
 * @param <Entity>
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseController<S extends BaseBiz, Entity> extends BaseResHandler {

    @Autowired
    protected S baseBiz;

    @Autowired
    private UserProvider userProvider;

    @Operation(summary = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Entity> save(@RequestBody Entity entity) {
        baseBiz.save(entity);
        return ok(entity);
    }

    @Operation(summary = "批量新增")
    @RequestMapping(value = "/saveBatch", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<List<Entity>> saveBatch(@RequestBody List<Entity> entityList) {
        baseBiz.saveBatch(entityList);
        return ok(entityList);
    }

    @Operation(summary = "id查询")
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ActionResult<Entity> getById(@PathVariable Serializable id) {
        Entity o = (Entity) baseBiz.getById(id);
        return ok(o);
    }

    @Operation(summary = "id查询")
    @RequestMapping(value = "/getDetailById/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ActionResult<Entity> getDetailById(@PathVariable Serializable id) {
        Entity o = (Entity) baseBiz.getDetailById(id);
        return ok(o);
    }

    @Operation(summary = "id批量查询")
    @RequestMapping(value = "/getByIds", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<List<Entity>> getByIds(@RequestBody List<Serializable> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ok(Collections.emptyList());
        }
        List<Entity> o = baseBiz.listByIds(ids);
        return ok(o);
    }

    @Operation(summary = "更新")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Entity> update(@RequestBody Entity entity) {
        baseBiz.updateById(entity);
        return ok();
    }

    @Operation(summary = "批量更新")
    @RequestMapping(value = "/updateBatch", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Boolean> updateBatch(@RequestBody List<Entity> entityList) {
        baseBiz.updateBatchById(entityList);
        return ok();
    }

    @Operation(summary = "保存")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Entity> saveOrUpdate(@RequestBody Entity entity) {
        baseBiz.saveOrUpdate(entity);
        return ok();
    }

    @Operation(summary = "批量保存")
    @RequestMapping(value = "/saveOrUpdateBatch", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Entity> saveOrUpdateBatch(@RequestBody List<Entity> entityList) {
        baseBiz.saveOrUpdateBatch(entityList);
        return ok();
    }

    @Operation(summary = "删除")
    @RequestMapping(value = "/remove/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public ActionResult<Entity> remove(@PathVariable Serializable id) {
        baseBiz.removeById(id);
        return ok();
    }

    @Operation(summary = "批量删除")
    @RequestMapping(value = "/removeBatchByIds", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Boolean> removeBatchByIds(@RequestBody List<Serializable> ids) {
        baseBiz.removeBatchByIds(ids);
        return ok();
    }

    @Operation(summary = "批量删除")
    @RequestMapping(value = "/removeByIds", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Boolean> removeByIds(@RequestBody List<Serializable> ids) {
        baseBiz.removeByIds(ids);
        return ok();
    }
 @Operation(summary = "批量删除")
    @RequestMapping(value = "/removeByQuery", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Boolean> removeByQuery(@RequestBody Map<String, Object> params) {
        baseBiz.removeByQuery(params);
        return ok();
    }


    @Operation(summary = "查询全部")
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    @ResponseBody
    public ActionResult<List<Entity>> all() {
        List<Entity> o = baseBiz.list();
        return ok(o);
    }

    @Operation(summary = "列表获取")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<List<Entity>> list(@RequestBody Map<String, Object> params) {
        List<Entity> list = baseBiz.listByMap(params);
        return ok(list);
    }

    @Operation(summary = "列表获取(不过滤空参数)")
    @RequestMapping(value = "/listByAllPara", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<List<Entity>> listByAllPara(@RequestBody Map<String, Object> params) {
        List<Entity> list = baseBiz.listByAllPara(params);
        return ok(list);
    }

    @Operation(summary = "计数")
    @RequestMapping(value = "/count", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<Long> count(@RequestBody Map<String, Object> params) {
        QueryParams query = new QueryParams(params);
        long count = baseBiz.count(baseBiz.parseQuery(query));
        return ok(count);
    }

    @Operation(summary = "分页查询")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<PageListVO<Entity>> page(@RequestBody Map<String, Object> params) {
        QueryParams query = new QueryParams(params);
        List<Entity> list = baseBiz.page(query);
        return ok(list, query);
    }

    @Operation(summary = "分页查询")
    @RequestMapping(value = "/minePage", method = RequestMethod.POST)
    @ResponseBody
    public ActionResult<PageListVO<Entity>> minePage(@RequestBody Map<String, Object> params) {
        params.put("creatorUserId", userProvider.get().getUserId());
        QueryParams query = new QueryParams(params);
        List<Entity> list = baseBiz.page(query);
        return ok(list, query);
    }

    @Operation(summary = "导出Excel")
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody Map<String, Object> params) throws IOException {
//        QueryParams params1 = new QueryParams(params);

        baseBiz.exportExcel(new QueryParams(params));
    }

}
